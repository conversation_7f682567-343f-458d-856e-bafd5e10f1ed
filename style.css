* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 10px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    /* Optimisations mobile */
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    touch-action: manipulation;
}

.container {
    max-width: 700px;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 10px;
    margin-bottom: 20px;
    /* Optimisations mobile */
    min-height: auto;
    overflow: hidden;
}

h1 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.8em;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    line-height: 1.2;
}

.controls-section {
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    align-items: stretch;
}

.unit-selector {
    flex: 1;
    min-width: 200px;
    padding: 15px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.notification-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.notification-btn {
    padding: 12px 20px;
    background: linear-gradient(145deg, #17a2b8, #138496);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    white-space: nowrap;
}

.notification-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.notification-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.notification-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    text-align: center;
    min-width: 100px;
}

.notification-status.granted {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.notification-status.denied {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.notification-status.default {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.unit-selector label {
    font-weight: 600;
    margin-right: 10px;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 1em;
    display: block;
}

.unit-selector select {
    padding: 14px 16px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    font-size: 16px; /* Évite le zoom sur iOS */
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 200px;
    /* Optimisations mobile */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

.unit-selector select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.inputs {
    display: flex;
    gap: 12px;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    padding: 0 10px;
}

.inputs input {
    padding: 16px 12px;
    width: 110px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    font-size: 16px; /* Évite le zoom sur iOS */
    text-align: center;
    transition: all 0.3s ease;
    background: white;
    /* Optimisations mobile */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    touch-action: manipulation;
}

.inputs input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.inputs input::placeholder {
    color: #6c757d;
    font-size: 14px;
}

.inputs button, .calc-btn {
    padding: 16px 20px;
    background: linear-gradient(145deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    min-height: 50px;
    /* Optimisations mobile */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
}

.inputs button:hover, .calc-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.inputs button:active, .calc-btn:active {
    transform: translateY(0px);
    transition: all 0.1s ease;
}

.calc-btn {
    width: 100%;
    max-width: 300px;
    margin: 20px auto;
    display: block;
    font-size: 18px;
    padding: 18px 25px;
}

.points-section {
    margin: 30px 0;
}

.points-section h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.4em;
    font-weight: 600;
}

#pointsList {
    list-style: none;
    padding: 0;
    margin-top: 15px;
    max-height: 250px;
    overflow-y: auto;
    /* Optimisations mobile */
    -webkit-overflow-scrolling: touch;
}

#pointsList li {
    margin: 10px 0;
    padding: 16px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    /* Optimisations mobile */
    touch-action: manipulation;
    word-break: break-word;
}

#pointsList li:active {
    transform: scale(0.98);
    transition: all 0.1s ease;
}

#pointsList li.empty {
    font-style: italic;
    color: #6c757d;
    justify-content: center;
    background: rgba(108, 117, 125, 0.1);
}

.point-actions {
    display: flex;
    gap: 10px;
}

.edit-btn, .delete-btn, .clear-btn {
    padding: 10px 14px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-height: 40px;
    min-width: 70px;
    /* Optimisations mobile */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
}

.edit-btn {
    background: linear-gradient(145deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.edit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.delete-btn {
    background: linear-gradient(145deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.clear-btn {
    background: linear-gradient(145deg, #6c757d, #545b62);
    color: white;
    margin-top: 20px;
    padding: 12px 20px;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

#resultat {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(145deg, #e8f5e8, #d4edda);
    border-radius: 15px;
    border: 1px solid rgba(40, 167, 69, 0.2);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
}

#resultat h3 {
    color: #155724;
    margin-bottom: 15px;
    font-size: 1.3em;
}

#resultat p {
    margin: 8px 0;
    color: #155724;
    font-size: 1.1em;
}

#resultat p strong {
    font-size: 1.3em;
    color: #0f5132;
}

/* === RESPONSIVE DESIGN POUR SMARTPHONES === */

/* Très petits écrans (smartphones en portrait) */
@media (max-width: 480px) {
    body {
        padding: 5px;
        align-items: flex-start;
    }

    .container {
        margin: 5px;
        padding: 15px;
        border-radius: 15px;
        max-width: 100%;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    h1 {
        font-size: 1.5em;
        margin-bottom: 20px;
    }

    .controls-section {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 15px;
    }

    .unit-selector {
        min-width: auto;
        padding: 12px;
        width: 100%;
    }

    .unit-selector label {
        font-size: 0.9em;
        margin-bottom: 6px;
    }

    .unit-selector select {
        font-size: 16px;
        padding: 12px 35px 12px 12px;
        width: 100%;
        max-width: none;
    }

    .notification-controls {
        width: 100%;
        align-items: stretch;
    }

    .notification-btn {
        width: 100%;
        padding: 14px 20px;
        font-size: 15px;
    }

    .inputs {
        flex-direction: column;
        gap: 12px;
        padding: 0;
    }

    .inputs input {
        width: 100%;
        max-width: none;
        padding: 16px;
        font-size: 16px;
    }

    .inputs button {
        width: 100%;
        padding: 16px;
        font-size: 16px;
    }

    .calc-btn {
        width: 100%;
        max-width: none;
        margin: 15px 0;
        padding: 18px;
        font-size: 17px;
    }

    .points-section h3 {
        font-size: 1.2em;
        margin-bottom: 15px;
    }

    #pointsList {
        max-height: 200px;
    }

    #pointsList li {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        padding: 14px;
        margin: 8px 0;
    }

    #pointsList li span {
        font-size: 14px;
        word-break: break-all;
    }

    .point-actions {
        justify-content: center;
        width: 100%;
        gap: 8px;
    }

    .edit-btn, .delete-btn {
        flex: 1;
        min-width: 80px;
        padding: 12px 8px;
        font-size: 13px;
    }

    .clear-btn {
        width: 100%;
        padding: 14px;
        font-size: 15px;
        margin-top: 15px;
    }

    #resultat {
        margin-top: 20px;
        padding: 15px;
        border-radius: 12px;
    }

    #resultat h3 {
        font-size: 1.1em;
        margin-bottom: 10px;
    }

    #resultat p {
        font-size: 1em;
        margin: 6px 0;
    }

    #resultat p strong {
        font-size: 1.1em;
    }
}

/* Tablettes et smartphones en paysage */
@media (min-width: 481px) and (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 20px;
        max-width: 90%;
    }

    .controls-section {
        flex-direction: column;
        gap: 15px;
    }

    .unit-selector {
        min-width: auto;
        width: 100%;
    }

    .notification-controls {
        width: 100%;
    }

    .notification-btn {
        width: 100%;
        max-width: 300px;
    }

    .inputs {
        gap: 15px;
    }

    .inputs input {
        width: 130px;
    }

    #pointsList li {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .point-actions {
        justify-content: center;
    }
}

/* Optimisations pour les écrans tactiles */
@media (hover: none) and (pointer: coarse) {
    .inputs button:hover,
    .calc-btn:hover,
    .edit-btn:hover,
    .delete-btn:hover,
    .clear-btn:hover,
    .notification-btn:hover {
        transform: none;
        box-shadow: inherit;
    }

    #pointsList li:hover {
        transform: none;
        box-shadow: inherit;
    }

    /* Augmenter la zone de touch pour les petits boutons */
    .edit-btn, .delete-btn {
        min-height: 44px;
        min-width: 80px;
    }
}

/* Animation pour les nouveaux éléments */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#pointsList li {
    animation: fadeInUp 0.3s ease;
}

/* === SYSTÈME DE NOTIFICATIONS === */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
    width: 100%;
}

.notification {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

/* Types de notifications */
.notification.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(255, 255, 255, 0.95));
}

.notification.error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(255, 255, 255, 0.95));
}

.notification.warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 255, 255, 0.95));
}

.notification.info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(255, 255, 255, 0.95));
}

/* Icônes des notifications */
.notification-icon {
    font-size: 24px;
    font-weight: bold;
    min-width: 24px;
    text-align: center;
}

.notification.success .notification-icon {
    color: #28a745;
}

.notification.error .notification-icon {
    color: #dc3545;
}

.notification.warning .notification-icon {
    color: #ffc107;
}

.notification.info .notification-icon {
    color: #17a2b8;
}

/* Contenu de la notification */
.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #2c3e50;
}

.notification-message {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

/* Bouton de fermeture */
.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #2c3e50;
}

/* Barre de progression */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 12px 12px;
    animation: notificationProgress 5s linear forwards;
}

.notification.success .notification-progress {
    background: #28a745;
}

.notification.error .notification-progress {
    background: #dc3545;
}

.notification.warning .notification-progress {
    background: #ffc107;
}

.notification.info .notification-progress {
    background: #17a2b8;
}

@keyframes notificationProgress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

/* Responsive pour les notifications */
@media (max-width: 768px) {
    #notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .notification {
        margin-bottom: 8px;
        padding: 14px 16px;
        border-radius: 10px;
    }

    .notification-title {
        font-size: 13px;
    }

    .notification-message {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    #notification-container {
        top: 5px;
        right: 5px;
        left: 5px;
    }

    .notification {
        padding: 12px 14px;
        margin-bottom: 6px;
        border-radius: 8px;
    }

    .notification-icon {
        font-size: 20px;
        min-width: 20px;
    }

    .notification-title {
        font-size: 12px;
        margin-bottom: 3px;
    }

    .notification-message {
        font-size: 11px;
        line-height: 1.3;
    }

    .notification-close {
        width: 18px;
        height: 18px;
        font-size: 16px;
    }
}
