* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    max-width: 700px;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

h1 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2.2em;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.controls-section {
    margin-bottom: 25px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.unit-selector {
    flex: 1;
    min-width: 250px;
    padding: 20px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.notification-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.notification-btn {
    padding: 12px 20px;
    background: linear-gradient(145deg, #17a2b8, #138496);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    white-space: nowrap;
}

.notification-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.notification-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.notification-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    text-align: center;
    min-width: 100px;
}

.notification-status.granted {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.notification-status.denied {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.notification-status.default {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.unit-selector label {
    font-weight: 600;
    margin-right: 15px;
    color: #2c3e50;
    font-size: 1.1em;
}

.unit-selector select {
    padding: 12px 16px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.unit-selector select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.inputs {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.inputs input {
    padding: 15px;
    width: 120px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    font-size: 16px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.inputs input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.inputs button, .calc-btn {
    padding: 15px 25px;
    background: linear-gradient(145deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.inputs button:hover, .calc-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.inputs button:active, .calc-btn:active {
    transform: translateY(-1px);
}

.points-section {
    margin: 30px 0;
}

.points-section h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.4em;
    font-weight: 600;
}

#pointsList {
    list-style: none;
    padding: 0;
    margin-top: 15px;
    max-height: 300px;
    overflow-y: auto;
}

#pointsList li {
    margin: 12px 0;
    padding: 18px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

#pointsList li:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

#pointsList li.empty {
    font-style: italic;
    color: #6c757d;
    justify-content: center;
    background: rgba(108, 117, 125, 0.1);
}

.point-actions {
    display: flex;
    gap: 10px;
}

.edit-btn, .delete-btn, .clear-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.edit-btn {
    background: linear-gradient(145deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.edit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.delete-btn {
    background: linear-gradient(145deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.clear-btn {
    background: linear-gradient(145deg, #6c757d, #545b62);
    color: white;
    margin-top: 20px;
    padding: 12px 20px;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

#resultat {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(145deg, #e8f5e8, #d4edda);
    border-radius: 15px;
    border: 1px solid rgba(40, 167, 69, 0.2);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
}

#resultat h3 {
    color: #155724;
    margin-bottom: 15px;
    font-size: 1.3em;
}

#resultat p {
    margin: 8px 0;
    color: #155724;
    font-size: 1.1em;
}

#resultat p strong {
    font-size: 1.3em;
    color: #0f5132;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 20px;
    }

    .controls-section {
        flex-direction: column;
        gap: 15px;
    }

    .unit-selector {
        min-width: auto;
    }

    .notification-controls {
        width: 100%;
    }

    .notification-btn {
        width: 100%;
        max-width: 250px;
    }

    .inputs {
        flex-direction: column;
        gap: 10px;
    }

    .inputs input {
        width: 100%;
        max-width: 200px;
    }

    #pointsList li {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .point-actions {
        justify-content: center;
    }
}

/* Animation pour les nouveaux éléments */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#pointsList li {
    animation: fadeInUp 0.3s ease;
}

/* === SYSTÈME DE NOTIFICATIONS === */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
    width: 100%;
}

.notification {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

/* Types de notifications */
.notification.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(255, 255, 255, 0.95));
}

.notification.error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(255, 255, 255, 0.95));
}

.notification.warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 255, 255, 0.95));
}

.notification.info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(255, 255, 255, 0.95));
}

/* Icônes des notifications */
.notification-icon {
    font-size: 24px;
    font-weight: bold;
    min-width: 24px;
    text-align: center;
}

.notification.success .notification-icon {
    color: #28a745;
}

.notification.error .notification-icon {
    color: #dc3545;
}

.notification.warning .notification-icon {
    color: #ffc107;
}

.notification.info .notification-icon {
    color: #17a2b8;
}

/* Contenu de la notification */
.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #2c3e50;
}

.notification-message {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

/* Bouton de fermeture */
.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #2c3e50;
}

/* Barre de progression */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 12px 12px;
    animation: notificationProgress 5s linear forwards;
}

.notification.success .notification-progress {
    background: #28a745;
}

.notification.error .notification-progress {
    background: #dc3545;
}

.notification.warning .notification-progress {
    background: #ffc107;
}

.notification.info .notification-progress {
    background: #17a2b8;
}

@keyframes notificationProgress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

/* Responsive pour les notifications */
@media (max-width: 768px) {
    #notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .notification {
        margin-bottom: 8px;
        padding: 14px 16px;
    }
}
