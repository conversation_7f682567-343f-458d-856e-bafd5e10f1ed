* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    max-width: 700px;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

h1 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2.2em;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.unit-selector {
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.unit-selector label {
    font-weight: 600;
    margin-right: 15px;
    color: #2c3e50;
    font-size: 1.1em;
}

.unit-selector select {
    padding: 12px 16px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.unit-selector select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.inputs {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.inputs input {
    padding: 15px;
    width: 120px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    font-size: 16px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.inputs input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.inputs button, .calc-btn {
    padding: 15px 25px;
    background: linear-gradient(145deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.inputs button:hover, .calc-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.inputs button:active, .calc-btn:active {
    transform: translateY(-1px);
}

.points-section {
    margin: 30px 0;
}

.points-section h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.4em;
    font-weight: 600;
}

#pointsList {
    list-style: none;
    padding: 0;
    margin-top: 15px;
    max-height: 300px;
    overflow-y: auto;
}

#pointsList li {
    margin: 12px 0;
    padding: 18px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

#pointsList li:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

#pointsList li.empty {
    font-style: italic;
    color: #6c757d;
    justify-content: center;
    background: rgba(108, 117, 125, 0.1);
}

.point-actions {
    display: flex;
    gap: 10px;
}

.edit-btn, .delete-btn, .clear-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.edit-btn {
    background: linear-gradient(145deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.edit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.delete-btn {
    background: linear-gradient(145deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.clear-btn {
    background: linear-gradient(145deg, #6c757d, #545b62);
    color: white;
    margin-top: 20px;
    padding: 12px 20px;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

#resultat {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(145deg, #e8f5e8, #d4edda);
    border-radius: 15px;
    border: 1px solid rgba(40, 167, 69, 0.2);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
}

#resultat h3 {
    color: #155724;
    margin-bottom: 15px;
    font-size: 1.3em;
}

#resultat p {
    margin: 8px 0;
    color: #155724;
    font-size: 1.1em;
}

#resultat p strong {
    font-size: 1.3em;
    color: #0f5132;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 20px;
    }

    .inputs {
        flex-direction: column;
        gap: 10px;
    }

    .inputs input {
        width: 100%;
        max-width: 200px;
    }

    #pointsList li {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .point-actions {
        justify-content: center;
    }
}

/* Animation pour les nouveaux éléments */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#pointsList li {
    animation: fadeInUp 0.3s ease;
}
