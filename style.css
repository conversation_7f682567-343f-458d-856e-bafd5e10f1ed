body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f4f4;
    text-align: center;
    padding: 20px;
}

.container {
    max-width: 600px;
    margin: auto;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.unit-selector {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.unit-selector label {
    font-weight: bold;
    margin-right: 10px;
}

.unit-selector select {
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #ccc;
    font-size: 14px;
}

.inputs input {
    padding: 10px;
    width: 80px;
    margin: 5px;
    border-radius: 8px;
    border: 1px solid #ccc;
}

.inputs button, .calc-btn {
    padding: 10px 15px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

.inputs button:hover, .calc-btn:hover {
    background-color: #45a049;
}

#pointsList {
    list-style: none;
    padding: 0;
    margin-top: 10px;
}

#pointsList li {
    margin: 5px 0;
}

#resultat {
    margin-top: 20px;
    font-size: 20px;
    font-weight: bold;
}
