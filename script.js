let points = [];

function ajouterPoint() {
    const x = parseFloat(document.getElementById('x').value);
    const y = parseFloat(document.getElementById('y').value);

    if (isNaN(x) || isNaN(y)) {
        alert("أدخل القيم بشكل صحيح");
        return;
    }

    points.push([x, y]);
    afficherPoints();
}

function afficherPoints() {
    const list = document.getElementById('pointsList');
    list.innerHTML = '';
    points.forEach((point, index) => {
        list.innerHTML += <li>Point ${index + 1}: (${point[0]}, ${point[1]})</li>;
    });
}

function calculerSurface() {
    if (points.length < 3) {
        alert("يجب إضافة على الأقل 3 نقاط");
        return;
    }

    let surface = 0;
    const n = points.length;

    for (let i = 0; i < n; i++) {
        const [x1, y1] = points[i];
        const [x2, y2] = points[(i + 1) % n];
        surface += (x1 * y2) - (y1 * x2);
    }

    surface = Math.abs(surface) / 2;
    document.getElementById('resultat').innerText = Surface: ${surface.toFixed(2)} m²;
}
