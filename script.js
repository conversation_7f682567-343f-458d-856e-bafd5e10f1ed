let points = [];
let uniteActuelle = 'm';
let editIndex = -1; // Index du point en cours de modification (-1 = pas de modification)

// === SYSTÈME DE NOTIFICATIONS ===
class NotificationManager {
    constructor() {
        this.container = document.getElementById('notification-container');
        this.notifications = [];
    }

    show(message, type = 'info', title = '', duration = 5000) {
        const notification = this.createNotification(message, type, title, duration);
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // Animation d'entrée
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto-suppression
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        return notification;
    }

    createNotification(message, type, title, duration) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const titles = {
            success: title || 'Succès',
            error: title || 'Erreur',
            warning: title || 'Attention',
            info: title || 'Information'
        };

        notification.innerHTML = `
            <div class="notification-icon">${icons[type] || icons.info}</div>
            <div class="notification-content">
                <div class="notification-title">${titles[type]}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="notificationManager.remove(this.parentElement)">×</button>
            ${duration > 0 ? '<div class="notification-progress"></div>' : ''}
        `;

        // Fermeture au clic
        notification.addEventListener('click', (e) => {
            if (!e.target.classList.contains('notification-close')) {
                this.remove(notification);
            }
        });

        return notification;
    }

    remove(notification) {
        if (!notification || !notification.parentElement) return;

        notification.classList.add('hide');

        setTimeout(() => {
            if (notification.parentElement) {
                notification.parentElement.removeChild(notification);
            }

            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 400);
    }

    success(message, title = '', duration = 4000) {
        return this.show(message, 'success', title, duration);
    }

    error(message, title = '', duration = 6000) {
        return this.show(message, 'error', title, duration);
    }

    warning(message, title = '', duration = 5000) {
        return this.show(message, 'warning', title, duration);
    }

    info(message, title = '', duration = 4000) {
        return this.show(message, 'info', title, duration);
    }

    clear() {
        this.notifications.forEach(notification => {
            this.remove(notification);
        });
    }
}

// Instance globale du gestionnaire de notifications
const notificationManager = new NotificationManager();

function ajouterPoint() {
    const x = parseFloat(document.getElementById('x').value);
    const y = parseFloat(document.getElementById('y').value);

    if (isNaN(x) || isNaN(y)) {
        notificationManager.error(
            "Veuillez entrer des valeurs numériques valides pour les coordonnées X et Y",
            "Valeurs invalides"
        );
        return;
    }

    if (editIndex >= 0) {
        // Mode modification
        points[editIndex] = [x, y];
        notificationManager.success(
            `Point ${editIndex + 1} modifié avec succès : (${x}, ${y}) ${uniteActuelle}`,
            "Point modifié"
        );
        editIndex = -1;
        document.querySelector('button[onclick="ajouterPoint()"]').textContent = 'Ajouter Point';
    } else {
        // Mode ajout
        points.push([x, y]);
        notificationManager.success(
            `Point ${points.length} ajouté : (${x}, ${y}) ${uniteActuelle}`,
            "Point ajouté"
        );
    }

    // Vider les champs
    document.getElementById('x').value = '';
    document.getElementById('y').value = '';

    afficherPoints();
}

function afficherPoints() {
    const list = document.getElementById('pointsList');
    list.innerHTML = '';

    if (points.length === 0) {
        list.innerHTML = '<li class="empty">Aucun point ajouté</li>';
        return;
    }

    points.forEach((point, index) => {
        const li = document.createElement('li');
        li.innerHTML = `
            <span>Point ${index + 1}: (${point[0]}, ${point[1]}) ${uniteActuelle}</span>
            <div class="point-actions">
                <button class="edit-btn" onclick="modifierPoint(${index})">Modifier</button>
                <button class="delete-btn" onclick="supprimerPoint(${index})">Supprimer</button>
            </div>
        `;
        list.appendChild(li);
    });
}

function calculerSurface() {
    if (points.length < 3) {
        notificationManager.warning(
            "Il faut au moins 3 points pour calculer une surface. Vous n'avez que " + points.length + " point(s).",
            "Points insuffisants"
        );
        return;
    }

    let surface = 0;
    const n = points.length;

    // Formule de Shoelace (lacet) pour calculer l'aire d'un polygone
    for (let i = 0; i < n; i++) {
        const [x1, y1] = points[i];
        const [x2, y2] = points[(i + 1) % n];
        surface += (x1 * y2) - (y1 * x2);
    }

    surface = Math.abs(surface) / 2;

    // Obtenir l'unité de surface appropriée
    const uniteSurface = obtenirUniteSurface();

    document.getElementById('resultat').innerHTML = `
        <h3>Résultat :</h3>
        <p><strong>Surface: ${surface.toFixed(4)} ${uniteSurface}</strong></p>
        <p>Nombre de points: ${points.length}</p>
    `;

    // Notification de succès
    notificationManager.success(
        `Surface calculée avec succès : ${surface.toFixed(4)} ${uniteSurface}`,
        "Calcul terminé"
    );
}

// Fonction pour modifier un point existant
function modifierPoint(index) {
    const point = points[index];
    document.getElementById('x').value = point[0];
    document.getElementById('y').value = point[1];
    editIndex = index;
    document.querySelector('button[onclick="ajouterPoint()"]').textContent = 'Confirmer Modification';
}

// Fonction pour supprimer un point
function supprimerPoint(index) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le point ${index + 1} ?`)) {
        points.splice(index, 1);
        afficherPoints();
        // Effacer le résultat si il y a moins de 3 points
        if (points.length < 3) {
            document.getElementById('resultat').innerHTML = '';
        }
    }
}

// Fonction pour vider tous les points
function viderPoints() {
    if (points.length > 0 && confirm('Êtes-vous sûr de vouloir supprimer tous les points ?')) {
        points = [];
        editIndex = -1;
        document.getElementById('x').value = '';
        document.getElementById('y').value = '';
        document.querySelector('button[onclick="ajouterPoint()"]').textContent = 'Ajouter Point';
        afficherPoints();
        document.getElementById('resultat').innerHTML = '';
    }
}

// Fonction pour changer l'unité de mesure
function changerUnite() {
    uniteActuelle = document.getElementById('unite').value;

    // Mettre à jour les placeholders
    const xInput = document.getElementById('x');
    const yInput = document.getElementById('y');
    xInput.placeholder = `X (${uniteActuelle})`;
    yInput.placeholder = `Y (${uniteActuelle})`;

    // Réafficher les points avec la nouvelle unité
    afficherPoints();
}

// Fonction pour obtenir l'unité de surface correspondante
function obtenirUniteSurface() {
    const unites = {
        'm': 'm²',
        'cm': 'cm²',
        'mm': 'mm²',
        'km': 'km²',
        'ft': 'ft²',
        'in': 'in²'
    };
    return unites[uniteActuelle] || 'm²';
}
