let points = [];
let uniteActuelle = 'm';
let editIndex = -1; // Index du point en cours de modification (-1 = pas de modification)

// === SYSTÈME DE NOTIFICATIONS ===
class NotificationManager {
    constructor() {
        this.container = document.getElementById('notification-container');
        this.notifications = [];
    }

    show(message, type = 'info', title = '', duration = 5000) {
        const notification = this.createNotification(message, type, title, duration);
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // Animation d'entrée
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto-suppression
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        return notification;
    }

    createNotification(message, type, title, duration) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const titles = {
            success: title || 'Succès',
            error: title || 'Erreur',
            warning: title || 'Attention',
            info: title || 'Information'
        };

        notification.innerHTML = `
            <div class="notification-icon">${icons[type] || icons.info}</div>
            <div class="notification-content">
                <div class="notification-title">${titles[type]}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="notificationManager.remove(this.parentElement)">×</button>
            ${duration > 0 ? '<div class="notification-progress"></div>' : ''}
        `;

        // Fermeture au clic
        notification.addEventListener('click', (e) => {
            if (!e.target.classList.contains('notification-close')) {
                this.remove(notification);
            }
        });

        return notification;
    }

    remove(notification) {
        if (!notification || !notification.parentElement) return;

        notification.classList.add('hide');

        setTimeout(() => {
            if (notification.parentElement) {
                notification.parentElement.removeChild(notification);
            }

            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 400);
    }

    success(message, title = '', duration = 4000) {
        return this.show(message, 'success', title, duration);
    }

    error(message, title = '', duration = 6000) {
        return this.show(message, 'error', title, duration);
    }

    warning(message, title = '', duration = 5000) {
        return this.show(message, 'warning', title, duration);
    }

    info(message, title = '', duration = 4000) {
        return this.show(message, 'info', title, duration);
    }

    clear() {
        this.notifications.forEach(notification => {
            this.remove(notification);
        });
    }
}

// Instance globale du gestionnaire de notifications
const notificationManager = new NotificationManager();

// === NOTIFICATIONS POPUP DU NAVIGATEUR ===
class BrowserNotificationManager {
    constructor() {
        this.permission = 'default';
        this.init();
    }

    async init() {
        // Vérifier si les notifications sont supportées
        if (!('Notification' in window)) {
            console.warn('Ce navigateur ne supporte pas les notifications desktop');
            return;
        }

        // Demander la permission si pas encore accordée
        if (Notification.permission === 'default') {
            this.permission = await Notification.requestPermission();
        } else {
            this.permission = Notification.permission;
        }
    }

    async requestPermission() {
        if (!('Notification' in window)) {
            notificationManager.warning(
                'Votre navigateur ne supporte pas les notifications desktop',
                'Notifications non supportées'
            );
            return false;
        }

        if (this.permission === 'granted') {
            return true;
        }

        const permission = await Notification.requestPermission();
        this.permission = permission;

        if (permission === 'granted') {
            notificationManager.success(
                'Notifications desktop activées avec succès !',
                'Permissions accordées'
            );
            return true;
        } else if (permission === 'denied') {
            notificationManager.error(
                'Notifications desktop refusées. Vous pouvez les activer dans les paramètres du navigateur.',
                'Permissions refusées'
            );
            return false;
        } else {
            notificationManager.info(
                'Permissions de notification en attente',
                'Action requise'
            );
            return false;
        }
    }

    show(title, options = {}) {
        if (!('Notification' in window)) {
            return null;
        }

        if (this.permission !== 'granted') {
            console.warn('Permission de notification non accordée');
            return null;
        }

        const defaultOptions = {
            icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01aDNWOGg0djRoM2wtNSA1eiIvPgo8L3N2Zz4KPC9zdmc+',
            badge: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjgiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB4PSI0IiB5PSI0IiB3aWR0aD0iOCIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIGZpbGw9IndoaXRlIj4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0yIDE1bC01LTVoM1Y4aDR2NGgzbC01IDV6IiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+',
            dir: 'ltr',
            lang: 'fr',
            renotify: false,
            requireInteraction: false,
            silent: false,
            tag: 'calcul-surface',
            timestamp: Date.now(),
            ...options
        };

        try {
            const notification = new Notification(title, defaultOptions);

            // Auto-fermeture après 5 secondes si pas d'interaction requise
            if (!defaultOptions.requireInteraction) {
                setTimeout(() => {
                    notification.close();
                }, 5000);
            }

            // Événements
            notification.onclick = () => {
                window.focus();
                notification.close();
                if (options.onClick) {
                    options.onClick();
                }
            };

            notification.onclose = () => {
                if (options.onClose) {
                    options.onClose();
                }
            };

            notification.onerror = (error) => {
                console.error('Erreur notification:', error);
                if (options.onError) {
                    options.onError(error);
                }
            };

            return notification;
        } catch (error) {
            console.error('Erreur lors de la création de la notification:', error);
            return null;
        }
    }

    success(title, body, options = {}) {
        return this.show(title, {
            body,
            icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiMyOGE3NDUiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik05IDE2LjE3TDQuODMgMTJsLTEuNDIgMS40MUw5IDE5IDIxIDdsLTEuNDEtMS40MXoiLz4KPC9zdmc+Cjwvc3ZnPg==',
            ...options
        });
    }

    error(title, body, options = {}) {
        return this.show(title, {
            body,
            icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiNkYzM1NDUiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xOSA2LjQxTDE3LjU5IDUgMTIgMTAuNTkgNi40MSA1IDUgNi40MUwxMC41OSAxMiA1IDE3LjU5IDYuNDEgMTlMMTIgMTMuNDFMMTcuNTkgMTkgMTkgMTcuNTlMMTMuNDEgMTJ6Ii8+Cjwvc3ZnPgo8L3N2Zz4=',
            requireInteraction: true,
            ...options
        });
    }

    warning(title, body, options = {}) {
        return this.show(title, {
            body,
            icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiNmZmMxMDciLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJibGFjayI+CjxwYXRoIGQ9Ik0xIDIxaDIyTDEyIDJMMSAyMXptMTItM2gtMnYtMmgydjJ6bTAtNGgtMlY5aDJ2NXoiLz4KPC9zdmc+Cjwvc3ZnPg==',
            ...options
        });
    }

    info(title, body, options = {}) {
        return this.show(title, {
            body,
            icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiMxN2EyYjgiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0xIDE1aC0ydi02aDJ2NnptMC04aC0yVjdoMnYyeiIvPgo8L3N2Zz4KPC9zdmc+',
            ...options
        });
    }

    isSupported() {
        return 'Notification' in window;
    }

    getPermission() {
        return this.permission;
    }
}

// Instance globale du gestionnaire de notifications navigateur
const browserNotificationManager = new BrowserNotificationManager();

// === FONCTIONS DE GESTION DES NOTIFICATIONS ===
async function activerNotificationsNavigateur() {
    const success = await browserNotificationManager.requestPermission();
    mettreAJourStatutNotifications();
    return success;
}

function mettreAJourStatutNotifications() {
    const button = document.getElementById('enable-notifications');
    const status = document.getElementById('notification-status');
    const permission = browserNotificationManager.getPermission();

    switch (permission) {
        case 'granted':
            button.textContent = '🔔 Notifications activées';
            button.disabled = true;
            status.textContent = 'Activées';
            status.className = 'notification-status granted';
            break;
        case 'denied':
            button.textContent = '🔕 Notifications refusées';
            button.disabled = true;
            status.textContent = 'Refusées';
            status.className = 'notification-status denied';
            break;
        default:
            button.textContent = '🔔 Activer les notifications';
            button.disabled = false;
            status.textContent = 'Non activées';
            status.className = 'notification-status default';
    }
}

// Fonction pour envoyer une notification (intégrée + popup)
function envoyerNotification(type, titre, message, options = {}) {
    // Notification intégrée (toujours affichée)
    notificationManager[type](message, titre);

    // Notification popup du navigateur (si autorisée)
    if (browserNotificationManager.getPermission() === 'granted') {
        browserNotificationManager[type](titre, message, options);
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    mettreAJourStatutNotifications();

    // Vérifier périodiquement le statut des notifications
    setInterval(mettreAJourStatutNotifications, 5000);
});

function ajouterPoint() {
    const x = parseFloat(document.getElementById('x').value);
    const y = parseFloat(document.getElementById('y').value);

    if (isNaN(x) || isNaN(y)) {
        envoyerNotification(
            'error',
            'Valeurs invalides',
            "Veuillez entrer des valeurs numériques valides pour les coordonnées X et Y"
        );
        return;
    }

    if (editIndex >= 0) {
        // Mode modification
        points[editIndex] = [x, y];
        envoyerNotification(
            'success',
            'Point modifié',
            `Point ${editIndex + 1} modifié : (${x}, ${y}) ${uniteActuelle}`
        );
        editIndex = -1;
        document.querySelector('button[onclick="ajouterPoint()"]').textContent = 'Ajouter Point';
    } else {
        // Mode ajout
        points.push([x, y]);
        envoyerNotification(
            'success',
            'Point ajouté',
            `Point ${points.length} ajouté : (${x}, ${y}) ${uniteActuelle}`
        );
    }

    // Vider les champs
    document.getElementById('x').value = '';
    document.getElementById('y').value = '';

    afficherPoints();
}

function afficherPoints() {
    const list = document.getElementById('pointsList');
    list.innerHTML = '';

    if (points.length === 0) {
        list.innerHTML = '<li class="empty">Aucun point ajouté</li>';
        return;
    }

    points.forEach((point, index) => {
        const li = document.createElement('li');
        li.innerHTML = `
            <span>Point ${index + 1}: (${point[0]}, ${point[1]}) ${uniteActuelle}</span>
            <div class="point-actions">
                <button class="edit-btn" onclick="modifierPoint(${index})">Modifier</button>
                <button class="delete-btn" onclick="supprimerPoint(${index})">Supprimer</button>
            </div>
        `;
        list.appendChild(li);
    });
}

function calculerSurface() {
    if (points.length < 3) {
        envoyerNotification(
            'warning',
            'Points insuffisants',
            `Il faut au moins 3 points pour calculer une surface. Vous n'avez que ${points.length} point(s).`
        );
        return;
    }

    let surface = 0;
    const n = points.length;

    // Formule de Shoelace (lacet) pour calculer l'aire d'un polygone
    for (let i = 0; i < n; i++) {
        const [x1, y1] = points[i];
        const [x2, y2] = points[(i + 1) % n];
        surface += (x1 * y2) - (y1 * x2);
    }

    surface = Math.abs(surface) / 2;

    // Obtenir l'unité de surface appropriée
    const uniteSurface = obtenirUniteSurface();

    document.getElementById('resultat').innerHTML = `
        <h3>Résultat :</h3>
        <p><strong>Surface: ${surface.toFixed(4)} ${uniteSurface}</strong></p>
        <p>Nombre de points: ${points.length}</p>
    `;

    // Notification de succès avec popup important
    envoyerNotification(
        'success',
        'Calcul terminé',
        `Surface calculée : ${surface.toFixed(4)} ${uniteSurface}`,
        {
            requireInteraction: true, // Nécessite une interaction pour fermer la popup
            onClick: () => {
                // Copier le résultat dans le presse-papiers
                navigator.clipboard.writeText(`${surface.toFixed(4)} ${uniteSurface}`).then(() => {
                    notificationManager.info('Résultat copié dans le presse-papiers', 'Copié');
                });
            }
        }
    );
}

// Fonction pour modifier un point existant
function modifierPoint(index) {
    const point = points[index];
    document.getElementById('x').value = point[0];
    document.getElementById('y').value = point[1];
    editIndex = index;
    document.querySelector('button[onclick="ajouterPoint()"]').textContent = 'Confirmer Modification';
}

// Fonction pour supprimer un point
function supprimerPoint(index) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le point ${index + 1} ?`)) {
        const point = points[index];
        points.splice(index, 1);

        envoyerNotification(
            'info',
            'Point supprimé',
            `Point ${index + 1} supprimé : (${point[0]}, ${point[1]}) ${uniteActuelle}`
        );

        afficherPoints();

        // Effacer le résultat si il y a moins de 3 points
        if (points.length < 3) {
            document.getElementById('resultat').innerHTML = '';
        }
    }
}

// Fonction pour vider tous les points
function viderPoints() {
    if (points.length > 0 && confirm('Êtes-vous sûr de vouloir supprimer tous les points ?')) {
        const nombrePoints = points.length;
        points = [];
        editIndex = -1;
        document.getElementById('x').value = '';
        document.getElementById('y').value = '';
        document.querySelector('button[onclick="ajouterPoint()"]').textContent = 'Ajouter Point';
        afficherPoints();
        document.getElementById('resultat').innerHTML = '';

        envoyerNotification(
            'warning',
            'Points supprimés',
            `Tous les points ont été supprimés (${nombrePoints} points)`
        );
    }
}

// Fonction pour changer l'unité de mesure
function changerUnite() {
    const ancienneUnite = uniteActuelle;
    uniteActuelle = document.getElementById('unite').value;

    // Mettre à jour les placeholders
    const xInput = document.getElementById('x');
    const yInput = document.getElementById('y');
    xInput.placeholder = `X (${uniteActuelle})`;
    yInput.placeholder = `Y (${uniteActuelle})`;

    // Réafficher les points avec la nouvelle unité
    afficherPoints();

    // Notification de changement d'unité
    if (ancienneUnite !== uniteActuelle) {
        envoyerNotification(
            'info',
            'Unité modifiée',
            `Unité de mesure changée de ${ancienneUnite} vers ${uniteActuelle}`
        );
    }
}

// Fonction pour obtenir l'unité de surface correspondante
function obtenirUniteSurface() {
    const unites = {
        'm': 'm²',
        'cm': 'cm²',
        'mm': 'mm²',
        'km': 'km²',
        'ft': 'ft²',
        'in': 'in²'
    };
    return unites[uniteActuelle] || 'm²';
}
