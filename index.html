<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Calcul Surface Polygon</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Calculateur de Surface</h1>

        <div class="unit-selector">
            <label for="unite">Unité de mesure :</label>
            <select id="unite" onchange="changerUnite()">
                <option value="m">Mètres (m)</option>
                <option value="cm">Centimètres (cm)</option>
                <option value="mm">Millimètres (mm)</option>
                <option value="km">Kilomètres (km)</option>
                <option value="ft">Pieds (ft)</option>
                <option value="in">Pouces (in)</option>
            </select>
        </div>

        <div class="inputs">
            <input type="number" id="x" placeholder="X (m)" step="any">
            <input type="number" id="y" placeholder="Y (m)" step="any">
            <button onclick="ajouterPoint()">Ajouter Point</button>
        </div>
        <div class="points-section">
            <h3>Points ajoutés :</h3>
            <ul id="pointsList"></ul>
            <button class="clear-btn" onclick="viderPoints()">Vider tous les points</button>
        </div>

        <button class="calc-btn" onclick="calculerSurface()">Calculer Surface</button>
        <div id="resultat"></div>
    </div>
    <script src="script.js"></script>
</body>
</html>